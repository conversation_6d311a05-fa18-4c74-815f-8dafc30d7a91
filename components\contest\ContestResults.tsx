'use client';

import { useState, useEffect } from 'react';
import { FiTrophy, FiMedal, FiAward, FiRefreshCw, FiHome, FiShare2 } from 'react-icons/fi';

interface Participant {
  id: string;
  name: string;
  progress: number;
  wpm: number;
  accuracy: number;
  position: number;
  isFinished: boolean;
}

interface ContestResultsProps {
  participants: Participant[];
  onBackToLobby: () => void;
  onPlayAgain: () => void;
}

export default function ContestResults({ participants, onBackToLobby, onPlayAgain }: ContestResultsProps) {
  const [sortedParticipants, setSortedParticipants] = useState<Participant[]>([]);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    // Sort participants by WPM (primary) and accuracy (secondary)
    const sorted = [...participants].sort((a, b) => {
      if (b.wpm !== a.wpm) return b.wpm - a.wpm;
      return b.accuracy - a.accuracy;
    });
    
    // Update positions
    const withPositions = sorted.map((p, index) => ({ ...p, position: index + 1 }));
    setSortedParticipants(withPositions);
    
    // Show confetti animation
    setShowConfetti(true);
    setTimeout(() => setShowConfetti(false), 3000);
  }, [participants]);

  const getPodiumHeight = (position: number) => {
    switch (position) {
      case 1: return 'h-32';
      case 2: return 'h-24';
      case 3: return 'h-20';
      default: return 'h-16';
    }
  };

  const getPodiumColor = (position: number) => {
    switch (position) {
      case 1: return 'from-yellow-400 to-yellow-600';
      case 2: return 'from-gray-300 to-gray-500';
      case 3: return 'from-orange-400 to-orange-600';
      default: return 'from-gray-600 to-gray-800';
    }
  };

  const getMedalIcon = (position: number) => {
    switch (position) {
      case 1: return <FiTrophy className="w-8 h-8 text-yellow-400" />;
      case 2: return <FiMedal className="w-8 h-8 text-gray-400" />;
      case 3: return <FiAward className="w-8 h-8 text-orange-400" />;
      default: return null;
    }
  };

  const handleShare = async () => {
    const winner = sortedParticipants[0];
    const shareText = `🏆 Contest Results!\n\n1st Place: ${winner.name} - ${winner.wpm} WPM (${winner.accuracy}% accuracy)\n\nJoin the typing contest at typing.com.co!`;
    
    try {
      await navigator.clipboard.writeText(shareText);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy results:', err);
    }
  };

  return (
    <div className="space-y-8">
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-50">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-orange-400 animate-bounce"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>
      )}

      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">
          🏆 Contest <span className="text-orange-400">Results</span>
        </h1>
        <p className="text-gray-400 text-lg">Congratulations to all participants!</p>
      </div>

      {/* Podium */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-8">
        <h2 className="text-2xl font-bold text-white text-center mb-8">Top 3 Winners</h2>
        
        <div className="flex justify-center items-end gap-4 mb-8">
          {sortedParticipants.slice(0, 3).map((participant, index) => {
            const actualPosition = participant.position;
            const displayOrder = actualPosition === 1 ? 1 : actualPosition === 2 ? 0 : 2; // Center 1st place
            
            return (
              <div
                key={participant.id}
                className={`flex flex-col items-center ${displayOrder === 1 ? 'order-2' : displayOrder === 0 ? 'order-1' : 'order-3'}`}
                style={{ animationDelay: `${index * 0.5}s` }}
              >
                {/* Medal */}
                <div className="mb-4">
                  {getMedalIcon(actualPosition)}
                </div>
                
                {/* Avatar */}
                <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${getPodiumColor(actualPosition)} flex items-center justify-center text-white font-bold text-xl mb-3 shadow-lg`}>
                  {participant.name[0].toUpperCase()}
                </div>
                
                {/* Name */}
                <div className="text-white font-semibold text-center mb-2">
                  {participant.name}
                  {participant.id === '1' && (
                    <span className="block text-xs text-blue-400">(You)</span>
                  )}
                </div>
                
                {/* Stats */}
                <div className="text-center text-sm text-gray-300 mb-4">
                  <div className="font-bold text-lg text-orange-400">{participant.wpm} WPM</div>
                  <div>{participant.accuracy}% accuracy</div>
                </div>
                
                {/* Podium Base */}
                <div className={`w-24 ${getPodiumHeight(actualPosition)} bg-gradient-to-t ${getPodiumColor(actualPosition)} rounded-t-lg flex items-end justify-center pb-2 shadow-lg`}>
                  <span className="text-white font-bold text-2xl">{actualPosition}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Detailed Rankings */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-6">Complete Rankings</h3>
        
        <div className="space-y-3">
          {sortedParticipants.map((participant, index) => (
            <div
              key={participant.id}
              className={`flex items-center justify-between p-4 rounded-lg transition-all ${
                participant.id === '1' 
                  ? 'bg-blue-500/20 border border-blue-500/30' 
                  : 'bg-gray-700/50 hover:bg-gray-700'
              }`}
            >
              <div className="flex items-center gap-4">
                {/* Position */}
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                  participant.position <= 3 
                    ? `bg-gradient-to-br ${getPodiumColor(participant.position)} text-white` 
                    : 'bg-gray-600 text-gray-300'
                }`}>
                  {participant.position}
                </div>
                
                {/* Medal for top 3 */}
                {participant.position <= 3 && (
                  <div className="flex-shrink-0">
                    {getMedalIcon(participant.position)}
                  </div>
                )}
                
                {/* Name */}
                <div>
                  <div className="text-white font-semibold">
                    {participant.name}
                    {participant.id === '1' && (
                      <span className="ml-2 px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded">YOU</span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Stats */}
              <div className="flex items-center gap-6 text-sm">
                <div className="text-center">
                  <div className="text-white font-bold text-lg">{participant.wpm}</div>
                  <div className="text-gray-400">WPM</div>
                </div>
                <div className="text-center">
                  <div className="text-white font-bold text-lg">{participant.accuracy}%</div>
                  <div className="text-gray-400">Accuracy</div>
                </div>
                <div className="text-center">
                  <div className="text-white font-bold text-lg">{participant.progress}%</div>
                  <div className="text-gray-400">Complete</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <button
          onClick={handleShare}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-semibold transition-all transform hover:scale-105"
        >
          <FiShare2 className="w-5 h-5" />
          Share Results
        </button>
        
        <button
          onClick={onPlayAgain}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-xl font-semibold transition-all transform hover:scale-105"
        >
          <FiRefreshCw className="w-5 h-5" />
          Play Again
        </button>
        
        <button
          onClick={onBackToLobby}
          className="flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white rounded-xl font-semibold transition-all transform hover:scale-105"
        >
          <FiHome className="w-5 h-5" />
          Back to Lobby
        </button>
      </div>
    </div>
  );
}
