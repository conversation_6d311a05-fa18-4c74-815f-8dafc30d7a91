'use client';

import { useEffect, useState } from 'react';
import { <PERSON><PERSON>lock, FiLogOut, FiPlay, FiShare2, FiUsers } from 'react-icons/fi';
import ContestResults from './ContestResults';
import ContestTypingInterface from './ContestTypingInterface';

interface ContestRoomProps {
  room: {
    id: string;
    name: string;
    createdBy: string;
    participants: string[];
    settings: {
      duration: number;
      difficulty: string;
      maxParticipants: number;
    };
    status: 'waiting' | 'active' | 'finished';
  } | null;
  onLeaveRoom: () => void;
}

interface Participant {
  id: string;
  name: string;
  progress: number;
  wpm: number;
  accuracy: number;
  position: number;
  isFinished: boolean;
}

export default function ContestRoom({ room, onLeaveRoom }: ContestRoomProps) {
  const [contestStatus, setContestStatus] = useState<
    'waiting' | 'countdown' | 'active' | 'finished'
  >('waiting');
  const [timeLeft, setTimeLeft] = useState(room?.settings.duration || 60);
  const [countdown, setCountdown] = useState(0);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [shareLink, setShareLink] = useState('');
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (room) {
      // Generate share link
      setShareLink(`${window.location.origin}/contest/join/${room.id}`);

      // Initialize participants
      const mockParticipants: Participant[] = [
        {
          id: '1',
          name: 'You',
          progress: 0,
          wpm: 0,
          accuracy: 100,
          position: 1,
          isFinished: false,
        },
        {
          id: '2',
          name: 'SpeedTyper',
          progress: 0,
          wpm: 0,
          accuracy: 100,
          position: 2,
          isFinished: false,
        },
        {
          id: '3',
          name: 'KeyboardNinja',
          progress: 0,
          wpm: 0,
          accuracy: 100,
          position: 3,
          isFinished: false,
        },
        {
          id: '4',
          name: 'TypeMaster',
          progress: 0,
          wpm: 0,
          accuracy: 100,
          position: 4,
          isFinished: false,
        },
      ];
      setParticipants(mockParticipants);
    }
  }, [room]);

  const startContest = () => {
    setContestStatus('countdown');
    setCountdown(3);

    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          setContestStatus('active');
          startTimer();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const startTimer = () => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          setContestStatus('finished');
          setShowResults(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(shareLink);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const updateParticipantProgress = (progress: number, wpm: number, accuracy: number) => {
    setParticipants(prev => prev.map(p => (p.id === '1' ? { ...p, progress, wpm, accuracy } : p)));
  };

  if (!room) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400 text-lg">Room not found</p>
        <button
          onClick={onLeaveRoom}
          className="mt-4 px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
        >
          Back to Lobby
        </button>
      </div>
    );
  }

  if (showResults) {
    return (
      <ContestResults
        participants={participants}
        onBackToLobby={onLeaveRoom}
        onPlayAgain={() => {
          setShowResults(false);
          setContestStatus('waiting');
          setTimeLeft(room.settings.duration);
          setParticipants(prev =>
            prev.map(p => ({ ...p, progress: 0, wpm: 0, accuracy: 100, isFinished: false })),
          );
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Room Header */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">{room.name}</h1>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <span className="flex items-center gap-1">
                <FiUsers className="w-4 h-4" />
                {participants.length}/{room.settings.maxParticipants}
              </span>
              <span className="flex items-center gap-1">
                <FiClock className="w-4 h-4" />
                {room.settings.duration}s
              </span>
              <span className="px-2 py-1 bg-orange-500/20 text-orange-400 rounded">
                {room.settings.difficulty}
              </span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={handleShare}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              <FiShare2 className="w-4 h-4" />
              Share
            </button>
            <button
              onClick={onLeaveRoom}
              className="flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
            >
              <FiLogOut className="w-4 h-4" />
              Leave
            </button>
          </div>
        </div>
      </div>

      {/* Contest Status */}
      <div className="text-center">
        {contestStatus === 'waiting' && (
          <div className="space-y-4">
            <h2 className="text-xl text-white">Waiting for contest to start...</h2>
            <button
              onClick={startContest}
              className="flex items-center gap-2 mx-auto px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-xl font-semibold transition-all transform hover:scale-105"
            >
              <FiPlay className="w-5 h-5" />
              Start Contest
            </button>
          </div>
        )}

        {contestStatus === 'countdown' && (
          <div className="space-y-4">
            <h2 className="text-xl text-white">Get Ready!</h2>
            <div className="text-6xl font-bold text-orange-400 animate-pulse">{countdown}</div>
          </div>
        )}

        {contestStatus === 'active' && (
          <div className="space-y-2">
            <h2 className="text-xl text-white">Contest in Progress</h2>
            <div className="text-3xl font-bold text-orange-400">{formatTime(timeLeft)}</div>
          </div>
        )}
      </div>

      {/* Participants List */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Participants</h3>
        <div className="space-y-3">
          {participants.map((participant, index) => (
            <div
              key={participant.id}
              className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  {index + 1}
                </div>
                <span className="text-white font-medium">{participant.name}</span>
                {participant.id === '1' && (
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded">
                    YOU
                  </span>
                )}
              </div>

              <div className="flex items-center gap-6 text-sm">
                <div className="text-center">
                  <div className="text-white font-bold">{participant.wpm}</div>
                  <div className="text-gray-400">WPM</div>
                </div>
                <div className="text-center">
                  <div className="text-white font-bold">{participant.accuracy}%</div>
                  <div className="text-gray-400">ACC</div>
                </div>
                <div className="w-24 bg-gray-600 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-orange-400 to-red-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${participant.progress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Real-time Graphs */}
      {(contestStatus === 'active' || contestStatus === 'countdown') && (
        <RealTimeGraphs
          participants={participants.map(p => ({
            id: p.id,
            name: p.name,
            wpm: p.wpm,
            accuracy: p.accuracy,
          }))}
          isActive={contestStatus === 'active'}
        />
      )}

      {/* Typing Interface */}
      {(contestStatus === 'active' || contestStatus === 'countdown') && (
        <ContestTypingInterface
          isActive={contestStatus === 'active'}
          difficulty={room.settings.difficulty}
          onProgress={updateParticipantProgress}
        />
      )}
    </div>
  );
}
